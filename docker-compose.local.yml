services:
  app:
    build:
      context: .
      dockerfile: .docker/local/Dockerfile
    container_name: st_student_app
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - .:/var/www
      - /var/www/node_modules
      - ./.docker/local/certs:/etc/nginx/certs  # Mount thư mục chứa chứng chỉ SSL
    ports:
      - "${APP_PORT:-9000}:9000"  # PHP-FPM
      - "${VITE_PORT:-5174}:5174"  # Vite frontend
    networks:
      - st_network
      - default
    env_file:
      - .env
    environment:
      - DOCKER=true  # Thêm biến môi trường này để nhận diện chạy trong Docker

  webserver:
    image: nginx:alpine
    container_name: st_student_nginx
    restart: unless-stopped
    ports:
      - "${NGINX_HTTP_PORT:-80}:80"   # HTTP
      - "${NGINX_HTTPS_PORT:-443}:443"  # HTTPS
    volumes:
      - .:/var/www
      - ./.docker/local/nginx/default.conf:/etc/nginx/conf.d/default.conf
      - ./.docker/local/certs:/etc/nginx/certs  # Mount thư mục chứa chứng chỉ SSL
    networks:
      - default
    depends_on:
      - app
    env_file:
      - .env

  db:
    image: mysql:8.0
    container_name: st_student_db
    restart: unless-stopped
    ports:
      - "${DB_PUBLISH_PORT:-3306}:3306"
    environment:
      MYSQL_DATABASE: ${MYSQL_DATABASE:-st_student_db}
      MYSQL_USER: ${MYSQL_USER:-st_user}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-st_password}
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-root_password}
    volumes:
      - dbdata:/var/lib/mysql
    networks:
      - default
    env_file:
      - .env

  queue:
    build:
      context: .
      dockerfile: .docker/local/Dockerfile
    container_name: st_student_queue
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - .:/var/www
    command: ["php", "artisan", "queue:work"]
    networks:
      - default
    depends_on:
      - db
      - app
    env_file:
      - .env

volumes:
  dbdata:
    driver: local

networks:
  st_network:
    driver: bridge
    external: true
  default:
    driver: bridge
