APP_NAME="Hệ thống quản lý sinh viên trực tuyến"
APP_ENV=local
APP_KEY=base64:WyFEghH+kKmX7VuCTe81Uu4h5INJEzdYDrWW3MY93zY=
APP_DEBUG=true
APP_URL=https://st.students.dev:8885

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=st_student_db
DB_PORT=3306
DB_DATABASE=st_tech_db
DB_USERNAME=root
DB_PASSWORD=root_password

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
# CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"
VITE_HOST=0.0.0.0
VITE_PORT=5174
VITE_SERVER=https://st.students.dev:5174
VITE_CLIENT=https://st.students.dev:5174

APP_PORT=9998
VITE_PORT=5174
NGINX_HTTP_PORT=8885

SSO_URL="http://localhost:8000"
SSO_CLIENT_ID=
SSO_CLIENT_SECRET=
SSO_API_KEY=
