<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class TrainingIndustry extends Model
{
    use HasFactory;

    protected $fillable = [
        'code',
        'name',
        'description',
        'faculty_id',
    ];

    /**
     * Quan hệ với Classes
     */
    public function classes(): HasMany
    {
        return $this->hasMany(ClassGenerate::class);
    }
}
