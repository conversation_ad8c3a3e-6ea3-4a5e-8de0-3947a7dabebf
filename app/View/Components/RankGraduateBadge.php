<?php

declare(strict_types=1);

namespace App\View\Components;

use App\Enums\RankGraduate;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class RankGraduateBadge extends Component
{
    /**
     * Create a new component instance.
     */
    public function __construct(
        public RankGraduate $rank
    ) {
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View
    {
        return view('components.rank-graduate-badge');
    }
}
