<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\TrainingIndustry;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class TrainingIndustryController extends Controller
{
    /**
     * Get all training industries.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = TrainingIndustry::with('faculty');

            // Filter by faculty_id if provided
            if ($request->has('faculty_id') && $request->faculty_id) {
                $query->where('faculty_id', $request->faculty_id);
            }

            // Search by name or code if provided
            if ($request->has('search') && $request->search) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('code', 'like', "%{$search}%");
                });
            }

            $trainingIndustries = $query->orderBy('name')->get();

            return response()->json([
                'success' => true,
                'data' => $trainingIndustries->map(function ($industry) {
                    return [
                        'id' => $industry->id,
                        'code' => $industry->code,
                        'name' => $industry->name,
                        'description' => $industry->description,
                        'faculty_id' => $industry->faculty_id,
                        'faculty' => $industry->faculty ? [
                            'id' => $industry->faculty->id,
                            'name' => $industry->faculty->name,
                        ] : null,
                        'created_at' => $industry->created_at,
                        'updated_at' => $industry->updated_at,
                    ];
                }),
            ]);
        } catch (Exception $e) {
            Log::error('Error fetching training industries for API: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Đã xảy ra lỗi khi lấy dữ liệu ngành đào tạo.',
            ], 500);
        }
    }

    /**
     * Get a specific training industry by ID.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        try {
            $trainingIndustry = TrainingIndustry::with('faculty')->find($id);

            if (!$trainingIndustry) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không tìm thấy ngành đào tạo.',
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $trainingIndustry->id,
                    'code' => $trainingIndustry->code,
                    'name' => $trainingIndustry->name,
                    'description' => $trainingIndustry->description,
                    'faculty_id' => $trainingIndustry->faculty_id,
                    'faculty' => $trainingIndustry->faculty ? [
                        'id' => $trainingIndustry->faculty->id,
                        'name' => $trainingIndustry->faculty->name,
                    ] : null,
                    'created_at' => $trainingIndustry->created_at,
                    'updated_at' => $trainingIndustry->updated_at,
                ],
            ]);
        } catch (Exception $e) {
            Log::error('Error fetching training industry for API: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Đã xảy ra lỗi khi lấy dữ liệu ngành đào tạo.',
            ], 500);
        }
    }

    /**
     * Get training industries by faculty ID.
     *
     * @param int $facultyId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getByFaculty(int $facultyId): JsonResponse
    {
        try {
            $trainingIndustries = TrainingIndustry::where('faculty_id', $facultyId)
                ->orderBy('name')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $trainingIndustries->map(function ($industry) {
                    return [
                        'id' => $industry->id,
                        'code' => $industry->code,
                        'name' => $industry->name,
                        'description' => $industry->description,
                        'faculty_id' => $industry->faculty_id,
                    ];
                }),
            ]);
        } catch (Exception $e) {
            Log::error('Error fetching training industries by faculty for API: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Đã xảy ra lỗi khi lấy dữ liệu ngành đào tạo theo khoa.',
            ], 500);
        }
    }
}
