<?php

namespace App\Http\Controllers\Api\External;

use App\Http\Controllers\Controller;
use App\Services\SsoService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class DepartmentController extends Controller
{
    protected $ssoService;

    public function __construct(
        private DepartmentService $departmentService
    )
    {
    }

    /**
     * Get all faculties data.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $departments = $this->departmentService->getDepartments();

        return response()->json([
            'success' => true,
            'data' => $departments,
        ]);
    }
} 