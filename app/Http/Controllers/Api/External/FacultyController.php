<?php

namespace App\Http\Controllers\Api\External;

use App\Http\Controllers\Controller;
use App\Services\SsoService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class FacultyController extends Controller
{
    protected $ssoService;

    public function __construct(SsoService $ssoService)
    {
        $this->ssoService = $ssoService;
    }

    /**
     * Get all faculties data.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(): JsonResponse
    {
        try {
            $faculties = cache()->remember(
                'api_faculties', 
                60 * 5, // Cache trong 5 phút
                fn () => $this->ssoService->get('/api/faculties/get-all')
            );

            return response()->json([
                'success' => true,
                'data' => $faculties['data'] ?? [],
            ]);
        } catch (Exception $e) {
            Log::error('Error fetching faculties for API: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while fetching data.',
            ], 500);
        }
    }
} 