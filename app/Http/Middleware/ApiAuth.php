<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ApiAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Kiểm tra xem có Bearer token không
        if (!$request->bearerToken()) {
            return response()->json([
                'success' => false,
                'message' => 'Token không được cung cấp.',
                'error' => 'Unauthorized'
            ], 401);
        }

        // Kiểm tra xem user đã được xác thực qua Passport chưa
        if (!auth('api')->check()) {
            return response()->json([
                'success' => false,
                'message' => 'Token không hợp lệ hoặc đã hết hạn.',
                'error' => 'Unauthorized'
            ], 401);
        }

        // Kiểm tra xem user có active không
        $user = auth('api')->user();
        if (!$user || $user->status !== 'active') {
            return response()->json([
                'success' => false,
                'message' => 'Tài khoản không hoạt động.',
                'error' => 'Forbidden'
            ], 403);
        }

        return $next($request);
    }
}
