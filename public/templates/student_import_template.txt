<PERSON><PERSON> <PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON><PERSON> chỉ,<PERSON><PERSON> b<PERSON>,<PERSON><PERSON><PERSON> b<PERSON>,<PERSON><PERSON> mẹ,<PERSON><PERSON><PERSON>ủ<PERSON> mẹ
NH001,<PERSON>001,<PERSON><PERSON><PERSON><PERSON>,01/01/2000,Nam,CNTT01,<PERSON><PERSON><PERSON> nghệ thông tin,2022-2026,<PERSON><PERSON>,0987654321,ng<PERSON><PERSON><PERSON>@example.com,<PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,0987654322,<PERSON><PERSON><PERSON><PERSON>,0987654323
NH002,SV002,<PERSON><PERSON><PERSON><PERSON>,02/02/2000,<PERSON><PERSON>,CNTT01,<PERSON><PERSON><PERSON> nghệ thông tin,2022-2026,<PERSON><PERSON>,0987654324,tranthi<PERSON>@example.com,<PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,0987654325,<PERSON><PERSON><PERSON><PERSON>,0987654326
NH003,<PERSON>003,<PERSON><PERSON>,03/03/2000,<PERSON>,<PERSON><PERSON><PERSON>02,<PERSON><PERSON><PERSON> thông tin,2022-2026,<PERSON><PERSON>,0987654327,<PERSON><PERSON><PERSON>@example.com,<PERSON><PERSON><PERSON><PERSON>,<PERSON>n <PERSON>,0987654328,<PERSON>ê Th<PERSON> G,0987654329

<PERSON>ướng dẫn:
1. <PERSON>ột A: Mã nhập h<PERSON>c
2. <PERSON>ột B: <PERSON>ã sinh viên (bắt buộc)
3. <PERSON>ột <PERSON>: <PERSON> tên (bắt buộc)
4. <PERSON>ột D: Ngày sinh (định dạng: DD/M<PERSON>/YYYY)
5. Cột E: Giới tính (Nam/Nữ)
6. Cột F: Lớp (bắt buộc) - Mã lớp trong hệ thống
7. Cột G: Khoa
8. Cột H: Niên khóa (định dạng: YYYY-YYYY)
9. Cột I: Dân tộc
10. Cột J: Điện thoại
11. Cột K: Email
12. Cột L: Địa chỉ
13. Cột M: Họ tên bố
14. Cột N: SĐT của bố
15. Cột O: Họ tên mẹ
16. Cột P: SĐT của mẹ

Lưu ý: Đây là file hướng dẫn. Vui lòng tạo file Excel (.xlsx, .xls) với cấu trúc tương tự.
