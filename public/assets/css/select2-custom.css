/* Custom styles for Select2 to match the application theme */

/* Match the height and appearance of form controls */
.select2-container--default .select2-selection--single {
    height: 38px;
    padding: 0.4375rem 0.875rem;
    font-size: 0.8125rem;
    line-height: 1.5385;
    color: var(--body-color);
    background-color: var(--body-bg);
    border: 1px solid var(--border-color);
    border-radius: 0.125rem;
}

/* Adjust the dropdown arrow position */
.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 36px;
    right: 12px;
    top: 18px;
}
.select2-container--default .select2-selection--single .select2-selection__arrow b {
    display: none;
}

/* Center the placeholder text vertically */
.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 24px;
    padding-left: 0;
    color: var(--body-color);
}

/* Style the dropdown */
.select2-container--default .select2-dropdown {
    border: 1px solid var(--border-color);
    border-radius: 0.125rem;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

/* Style the search field */
.select2-container--default .select2-search--dropdown .select2-search__field {
    border: 1px solid var(--border-color);
    border-radius: 0.125rem;
    padding: 0.4375rem 0.875rem;
}

/* Style the results */
.select2-container--default .select2-results__option {
    padding: 0.4375rem 0.875rem;
    font-size: 0.8125rem;
}

/* Style the highlighted result */
.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: #1376bc;
    color: white;
}

/* Style the selected option */
.select2-container--default .select2-results__option[aria-selected=true] {
    background-color: rgba(19, 118, 188, 0.1);
}

/* Fix for modal z-index issues */
.select2-container--open {
    z-index: 9999;
}

/* Styles for Select2 in modals */
.modal .select2-container--default .select2-selection--single {
    height: 38px;
    padding: 0.4375rem 0.875rem;
    font-size: 0.8125rem;
    line-height: 1.5385;
    border-radius: 0.125rem;
}

.modal .select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 24px;
    padding-left: 0;
}

.modal .select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 36px;
}

/* Custom classes for larger Select2 */
.select-lg {
    height: 38px;
}

.select2-dropdown-lg {
    font-size: 0.8125rem;
}

/* Fix for width issues */
.select2 {
    width: 100% !important;
}

/* Fix for placeholder color */
.select2-container--default .select2-selection--single .select2-selection__placeholder {
    color: #999;
}

/* Fix for focus state */
.select2-container--default.select2-container--focus .select2-selection--single,
.select2-container--default.select2-container--open .select2-selection--single {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Fix for disabled state */
.select2-container--default .select2-selection--single[aria-disabled=true] {
    background-color: #e9ecef;
    opacity: 1;
}

/* Fix for RTL support */
.select2-container--default[dir="rtl"] .select2-selection--single .select2-selection__arrow {
    left: 6px;
    right: auto;
}

/* Fix for clear button */
.select2-container--default .select2-selection--single .select2-selection__clear {
    margin-right: 10px;
    font-weight: bold;
    color: #999;
}

/* Fix for error state */
.is-invalid + .select2-container--default .select2-selection--single,
select.is-invalid ~ .select2-container--default .select2-selection--single {
    border-color: #dc3545;
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
