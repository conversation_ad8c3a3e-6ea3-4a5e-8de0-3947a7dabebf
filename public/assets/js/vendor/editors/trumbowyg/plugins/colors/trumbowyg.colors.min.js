/* ===========================================================
 * trumbowyg.colors.js v1.2
 * Colors picker plugin for Trumbowyg
 * http://alex-d.github.com/Trumbowyg
 * ===========================================================
 * Author : <PERSON> (Alex-D)
 *          Twitter : @AlexandreDemode
 *          Website : alex-d.fr
 */
!function(o){"use strict";function r(o){return("0"+parseInt(o).toString(16)).slice(-2)}function e(o){return-1===o.search("rgb")?o.replace("#",""):"rgba(0, 0, 0, 0)"===o||null==(o=o.match(/^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d?(.\d+)))?\)$/))?"transparent":r(o[1])+r(o[2])+r(o[3])}o.extend(!0,o.trumbowyg,{langs:{en:{foreColor:"Text color",backColor:"Background color",foreColorRemove:"Remove text color",backColorRemove:"Remove background color"},by:{foreColor:"Колер тэксту",backColor:"Колер фону тэксту",foreColorRemove:"Выдаліць колер тэксту",backColorRemove:"Выдаліць колер фону тэксту"},cs:{foreColor:"Barva textu",backColor:"Barva pozadí"},da:{foreColor:"Tekstfarve",backColor:"Baggrundsfarve"},de:{foreColor:"Textfarbe",backColor:"Hintergrundfarbe"},et:{foreColor:"Teksti värv",backColor:"Taustavärv",foreColorRemove:"Eemalda teksti värv",backColorRemove:"Eemalda taustavärv"},fr:{foreColor:"Couleur du texte",backColor:"Couleur de fond",foreColorRemove:"Supprimer la couleur du texte",backColorRemove:"Supprimer la couleur de fond"},hu:{foreColor:"Betű szín",backColor:"Háttér szín",foreColorRemove:"Betű szín eltávolítása",backColorRemove:"Háttér szín eltávolítása"},ja:{foreColor:"文字色",backColor:"背景色"},ko:{foreColor:"글자색",backColor:"배경색",foreColorRemove:"글자색 지우기",backColorRemove:"배경색 지우기"},nl:{foreColor:"Tekstkleur",backColor:"Achtergrondkleur"},pt_br:{foreColor:"Cor de fonte",backColor:"Cor de fundo"},ru:{foreColor:"Цвет текста",backColor:"Цвет выделения текста",foreColorRemove:"Очистить цвет текста",backColorRemove:"Очистить цвет выделения текста"},sk:{foreColor:"Farba textu",backColor:"Farba pozadia"},tr:{foreColor:"Yazı rengi",backColor:"Arka plan rengi",foreColorRemove:"Yazı rengini kaldır",backColorRemove:"Arka plan rengini kaldır"},zh_cn:{foreColor:"文字颜色",backColor:"背景颜色"},zh_tw:{foreColor:"文字顏色",backColor:"背景顏色"}}});var l={colorList:["ffffff","000000","eeece1","1f497d","4f81bd","c0504d","9bbb59","8064a2","4bacc6","f79646","ffff00","f2f2f2","7f7f7f","ddd9c3","c6d9f0","dbe5f1","f2dcdb","ebf1dd","e5e0ec","dbeef3","fdeada","fff2ca","d8d8d8","595959","c4bd97","8db3e2","b8cce4","e5b9b7","d7e3bc","ccc1d9","b7dde8","fbd5b5","ffe694","bfbfbf","3f3f3f","938953","548dd4","95b3d7","d99694","c3d69b","b2a2c7","b7dde8","fac08f","f2c314","a5a5a5","262626","494429","17365d","366092","953734","76923c","5f497a","92cddc","e36c09","c09100","7f7f7f","0c0c0c","1d1b10","0f243e","244061","632423","4f6128","3f3151","31859b","974806","7f6000"],foreColorList:null,backColorList:null,allowCustomForeColor:!0,allowCustomBackColor:!0,displayAsList:!1};function a(r,e){var l=[],a=e.o.plugins.colors,t=a[r+"List"]||a.colorList;o.each(t,(function(o,t){var c=r+t,f={fn:r,forceCss:!0,hasIcon:!1,text:e.lang["#"+t]||"#"+t,param:"#"+t,style:"background-color: #"+t+";"};a.displayAsList&&"foreColor"===r&&(f.style="color: #"+t+" !important;"),e.addBtnDef(c,f),l.push(c)}));var c=r+"Remove",f={fn:"removeFormat",hasIcon:!1,param:r,style:"background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAECAYAAACp8Z5+AAAAG0lEQVQIW2NkQAAfEJMRmwBYhoGBYQtMBYoAADziAp0jtJTgAAAAAElFTkSuQmCC);"};if(a.displayAsList&&(f.style=""),e.addBtnDef(c,f),l.push(c),a["allowCustom"+r.charAt(0).toUpperCase()+r.substr(1)]){var d=r+"Free",n={fn:function(){e.openModalInsert(e.lang[r],{color:{label:r,forceCss:!0,type:"color",value:"#FFFFFF"}},(function(o){return e.execCmd(r,o.color),!0}))},hasIcon:!1,text:"#",style:"text-indent: 0; line-height: 20px; padding: 0 5px;"};e.addBtnDef(d,n),l.push(d)}return l}o.extend(!0,o.trumbowyg,{plugins:{color:{init:function(o){o.o.plugins.colors=o.o.plugins.colors||l;var r=o.o.plugins.colors.displayAsList?o.o.prefix+"dropdown--color-list":"",e={dropdown:a("foreColor",o),dropdownClass:r},t={dropdown:a("backColor",o),dropdownClass:r};o.addBtnDef("foreColor",e),o.addBtnDef("backColor",t)},tagHandler:function(o,r){var l,a=[];if(!o.style)return a;if(""!==o.style.backgroundColor){var t=e(o.style.backgroundColor);r.o.plugins.colors.colorList.indexOf(t)>=0?a.push("backColor"+t):a.push("backColorFree")}return""!==o.style.color?l=e(o.style.color):o.hasAttribute("color")&&(l=e(o.getAttribute("color"))),l&&(r.o.plugins.colors.colorList.indexOf(l)>=0?a.push("foreColor"+l):a.push("foreColorFree")),a}}}})}(jQuery);