{"private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite", "prepare": "husky"}, "devDependencies": {"@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@tailwindcss/vite": "^4.0.0", "axios": "^1.7.4", "concurrently": "^9.0.1", "husky": "^9.1.7", "laravel-echo": "^2.1.6", "laravel-vite-plugin": "^1.2.0", "pusher-js": "^8.4.0", "sass": "^1.85.1", "sass-embedded": "^1.85.1", "tailwindcss": "^4.0.0", "vite": "^6.0.11"}}