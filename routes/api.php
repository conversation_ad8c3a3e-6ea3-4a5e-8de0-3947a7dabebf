<?php

declare(strict_types=1);

use App\Http\Controllers\Api\External\FacultyController;
use App\Http\Controllers\Api\TrainingIndustryController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/user', fn (Request $request) => $request->user())->middleware('auth:api');

// router create token

Route::prefix('v1/external')->group(function () {
    Route::prefix('/training-industries')->group(function () {
        Route::get('/', [TrainingIndustryController::class, 'index']);
        Route::get('/{id}', [TrainingIndustryController::class, 'show']);
        Route::get('/faculty/{facultyId}', [TrainingIndustryController::class, 'getByFaculty']);
    });
});
