<?php

declare(strict_types=1);

use App\Http\Controllers\Api\External\FacultyController;
use App\Http\Controllers\Api\TrainingIndustryController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/user', fn (Request $request) => $request->user())->middleware('auth:api');

Route::prefix('v1/external')->group(function () {
    Route::get('/faculties', [FacultyController::class, 'index']);
});

Route::prefix('v1')->group(function () {
    // Training Industries API
    Route::get('/training-industries', [TrainingIndustryController::class, 'index']);
    Route::get('/training-industries/{id}', [TrainingIndustryController::class, 'show']);
    Route::get('/training-industries/faculty/{facultyId}', [TrainingIndustryController::class, 'getByFaculty']);
});
