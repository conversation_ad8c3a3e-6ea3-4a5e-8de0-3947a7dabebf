# HTTP server - Redirect to HTTPS
server {
    listen 80;
    server_name st.students.dev;

    # # Redirect all HTTP traffic to HTTPS
    return 301 https://$host$request_uri;
}

# HTTPS server
server {
    listen 443 ssl;
    server_name st.sso.dev;

    # SSL configuration
    ssl_certificate /etc/nginx/certs/st.students.dev.pem;
    ssl_certificate_key /etc/nginx/certs/st.students.dev-key.pem;

    # Optional: Enforce HSTS (remove if you don’t want it yet)
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    root /var/www/public;
    index index.php index.html index.htm;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        include fastcgi_params;
        fastcgi_pass app:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    }

    # Proxy cho Vite Dev Server
    location /_vite/ {
        proxy_pass https://127.0.0.1:5174;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto https;

        # WebSocket support for HMR
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "Upgrade";

        # Nếu dùng chứng chỉ tự ký
        proxy_ssl_verify off;
    }
}
