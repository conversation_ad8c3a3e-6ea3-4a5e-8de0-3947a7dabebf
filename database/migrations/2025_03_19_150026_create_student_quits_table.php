<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('student_quits', function (Blueprint $table): void {
            $table->id();
            $table->unsignedBigInteger('quit_id')->nullable()->index();
            $table->string('note_quit');
            $table->unsignedBigInteger('student_id')->nullable()->index();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('student_quits');
    }
};
